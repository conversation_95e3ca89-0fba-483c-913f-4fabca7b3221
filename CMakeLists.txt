﻿cmake_minimum_required(VERSION 3.15)
file(STRINGS "${CMAKE_CURRENT_SOURCE_DIR}/version.txt" projectVersion)
project(ECU_Fmu VERSION ${projectVersion} LANGUAGES C CXX)

if (POLICY CMP0091)
    cmake_policy(SET CMP0091 NEW)
endif ()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_definitions(-D_WIN32_WINNT=0x0601 -DWIN32_LEAN_AND_MEAN)

set(BMS_LIB
${CMAKE_CURRENT_SOURCE_DIR}/bin
)

# Set binary directory
set(bin_dir "${CMAKE_CURRENT_SOURCE_DIR}/bin")

# Include path directories for BCC
set(BCC_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../BMS_ECU/BCC_Model")
get_filename_component(BCC_incl "${BCC_incl_path}" REALPATH)

# Include path directories for MCAL
set(MCAL_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../MCAL_Stub")
get_filename_component(MCAL_incl "${MCAL_incl_path}" REALPATH)

############ ModelIdentifier and export version ###########

set(modelIdentifier identity)
set(fmi_version fmi2) # fmi2 and fmi3

###########################################################

add_subdirectory(SystemC)

################ Ptotobuf implementation ##################

find_package(Boost REQUIRED COMPONENTS system)
find_package(Protobuf REQUIRED)

set(PROTO_DIR "${CMAKE_SOURCE_DIR}/proto")

protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${PROTO_DIR}/message.proto)

include_directories(
    ${Boost_INCLUDE_DIRS}
    ${Protobuf_INCLUDE_DIRS}
    ${CMAKE_CURRENT_BINARY_DIR}  
)

add_executable(Test Test/Test.cpp ${PROTO_SRCS} ${PROTO_HDRS})

target_link_libraries(Test PRIVATE
    Boost::system
    protobuf::libprotobuf
    SystemC::systemc
    ws2_32           
)

target_link_libraries(Test PRIVATE 
${CMAKE_DL_LIBS} 
${BMS_LIB}/libBCC_Model.lib
${BMS_LIB}/lib12V_BMS.lib 
${BMS_LIB}/libVINFAST_dll.lib
)

#########################################################

include("cmake/generate_fmu.cmake")

add_subdirectory(export)

