add_library(<PERSON><PERSON>_ECU SHARED
        "src/BMS_ECU.cpp"
)

set_target_properties(BMS_ECU PROPERTIES
    OUTPUT_NAME "BMS_ECU"
    PREFIX ""

    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/BMS_ECU/binaries/win32"
)

set(outputDir "${CMAKE_BINARY_DIR}/BMS_ECU/binaries/win32") # Only use for target platform win32 for MCU and Canoe

file(GLOB_RECURSE LIB_DIR 
${bin_dir}/*.dll
)

foreach(DLL ${LIB_DIR})
    add_custom_command(TARGET BMS_ECU POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${DLL}
                ${CMAKE_BINARY_DIR}
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${DLL}
                ${outputDir}
    )
endforeach()

message(STATUS "CMAKE_BINARY_DIR: ${CMA<PERSON>_BINARY_DIR}")

target_include_directories(B<PERSON>_ECU PUBLIC
# Include
${CMAKE_CURRENT_SOURCE_DIR}/include
${Boost_INCLUDE_DIRS}
${Protobuf_INCLUDE_DIRS}
${CMAKE_CURRENT_BINARY_DIR}  
# Include directories for BCC
${BCC_incl}/Modeling
${BCC_incl}/include
# Include directories for MCAL
${MCAL_incl}/inc
${MCAL_incl}/board
${MCAL_incl}/generate/include
${MCAL_incl}/RTD/include
${MCAL_incl}/User_Config/include
${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/include
${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/header
${MCAL_incl}/Platform_TS_T40D34M30I0R0/startup/include
${MCAL_incl}/Platform_TS_T40D34M30I0R0/include
)

if(MSVC)
    target_link_libraries (BMS_ECU PRIVATE
        SystemC::systemc 
        Boost::system
        protobuf::libprotobuf
        SystemC::systemc
        ws2_32
    ) 
    target_link_libraries (BMS_ECU PRIVATE   
        ${CMAKE_DL_LIBS}
        "${BMS_LIB}/libBCC_Model.lib" 
        "${BMS_LIB}/lib12V_BMS.lib" 
        "${BMS_LIB}/libVINFAST_dll.lib"
    )
else()
    target_link_libraries (BMS_ECU PRIVATE
        SystemC::systemc 
        Boost::system
        protobuf::libprotobuf
        SystemC::systemc
        ws2_32
    )   
    target_link_libraries (BMS_ECU PRIVATE 
        ${CMAKE_DL_LIBS} 
        "${BMS_LIB}/libBCC_Model.dll.a" 
        "${BMS_LIB}/lib12V_BMS.dll.a" 
        "${BMS_LIB}/libVINFAST_dll.dll.a"
    )
endif()


generateFMU("BMS_ECU" "${fmi_version}")
