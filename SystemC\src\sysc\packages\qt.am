## ****************************************************************************
##
##  Licensed to Accellera Systems Initiative Inc. (Accellera) under one or
##  more contributor license agreements.  See the NOTICE file distributed
##  with this work for additional information regarding copyright ownership.
##  Accellera licenses this file to you under the Apache License, Version 2.0
##  (the "License"); you may not use this file except in compliance with the
##  License.  You may obtain a copy of the License at
##
##   http://www.apache.org/licenses/LICENSE-2.0
##
##  Unless required by applicable law or agreed to in writing, software
##  distributed under the License is distributed on an "AS IS" BASIS,
##  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
##  implied.  See the License for the specific language governing
##  permissions and limitations under the License.
##
## ****************************************************************************
##
##  src/sysc/packages/qt.am --
##  Included from a Makefile.am to provide package-specific information
##
##  Original Author: Philipp A Hartmann, Intel
##
## ****************************************************************************
##
##  MODIFICATION LOG - modifiers, enter your name, affiliation, date and
##  changes you are making here.
##
##      Name, Affiliation, Date:
##  Description of Modification:
##
## ****************************************************************************

## Generic directory setup
## (should be kept in sync among all files/<package>.am files)
##
## Note: Recent Automake versions (>1.13) support relative placeholders for
##       included files (%D%,%C%).  To support older versions, use explicit
##       names for now.
##
## Local values:
##   %D%: packages
##   %C%: packages

QT_LIB    = packages/qt/libqt.la
QT_SUBDIR = packages/qt

# build external QT package separately
SUBDIRS += $(QT_SUBDIR)
LIBADD  += $(QT_LIB)

## Taf!
## :vim:ft=automake:
