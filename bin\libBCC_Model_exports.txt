Microsoft (R) COFF/PE Dumper Version 14.41.34120.0
Copyright (C) Microsoft Corporation.  All rights reserved.


Dump of file libBCC_Model.dll

File Type: DLL

  Section contains the following exports for libBCC_Model.dll

    00000000 characteristics
    6850EEB4 time date stamp Tue Jun 17 11:27:32 2025
        0.00 version
           1 ordinal base
          86 number of functions
          86 number of names

    ordinal hint RVA      name

          1    0 000037F7 BCC_CB_Enable
          2    1 00003941 BCC_CB_Pause
          3    2 0000386C BCC_CB_SetIndividual
          4    3 00003D39 BCC_EEPROM_Read
          5    4 00003EE0 BCC_EEPROM_Write
          6    5 00003452 BCC_Fault_ClearStatus
          7    6 0000332C BCC_Fault_GetStatus
          8    7 000039B6 BCC_FuseMirror_Read
          9    8 00003A9B BCC_FuseMirror_Write
         10    9 000036B4 BCC_GPIO_ReadPin
         11    A 000034E7 BCC_GPIO_SetMode
         12    B 00003756 BCC_GPIO_SetOutput
         13    C 00003BD6 BCC_GUID_Read
         14    D 0000237C BCC_HardwareReset
         15    E 00002053 BCC_Init
         16    F 00004BDE BCC_MCU_Assert
         17   10 00004C3C BCC_MCU_GetSystemClockFreq
         18   11 00004C2C BCC_MCU_ReadIntbPin
         19   12 00004B96 BCC_MCU_StartTimeout
         20   13 00004BA0 BCC_MCU_TimeoutExpired
         21   14 00004BAC BCC_MCU_TransferSpi
         22   15 00004BC4 BCC_MCU_TransferTpl
         23   16 00004C72 BCC_MCU_WaitMs
         24   17 00004C42 BCC_MCU_WaitSec
         25   18 00004C82 BCC_MCU_WaitUs
         26   19 00004BED BCC_MCU_WriteCsbPin
         27   1A 00004C17 BCC_MCU_WriteEnPin
         28   1B 00004C02 BCC_MCU_WriteRstPin
         29   1C 00003119 BCC_Meas_GetAnVoltage
         30   1D 0000302C BCC_Meas_GetAnVoltages
         31   1E 00002F46 BCC_Meas_GetCellVoltage
         32   1F 00002E0E BCC_Meas_GetCellVoltages
         33   20 00002B5A BCC_Meas_GetCoulombCounter
         34   21 000031DD BCC_Meas_GetIcTemperature
         35   22 00002C06 BCC_Meas_GetIsenseVoltage
         36   23 000029A0 BCC_Meas_GetRawValues
         37   24 00002D58 BCC_Meas_GetStackVoltage
         38   25 000027C9 BCC_Meas_IsConverting
         39   26 00002857 BCC_Meas_StartAndWait
         40   27 00002701 BCC_Meas_StartConversion
         41   28 00002770 BCC_Meas_StartConversionGlobal
         42   29 000024E3 BCC_Reg_Read
         43   2A 000044CE BCC_Reg_ReadSpi
         44   2B 00004335 BCC_Reg_ReadTpl
         45   2C 00002637 BCC_Reg_Update
         46   2D 00002567 BCC_Reg_Write
         47   2E 000025DE BCC_Reg_WriteGlobal
         48   2F 0000491F BCC_Reg_WriteGlobalTpl
         49   30 00004806 BCC_Reg_WriteSpi
         50   31 00004744 BCC_Reg_WriteTpl
         51   32 0000220B BCC_SendNop
         52   33 00004A85 BCC_SendNopSpi
         53   34 000049D2 BCC_SendNopTpl
         54   35 00002255 BCC_Sleep
         55   36 000022F6 BCC_SoftwareReset
         56   37 000024C0 BCC_TPL_Disable
         57   38 000023D5 BCC_TPL_Enable
         58   39 000022B9 BCC_WakeUp
         59   3A 00001761 BCC_processing
         60   3B 00004B90 LPIT0_Ch3_IRQHandler
         61   3C 0000C200 LPSPI_0_REG
         62   3D 0000DA00 LPSPI_1_REG
         63   3E 0000D200 LPSPI_2_REG
         64   3F 0000CA00 LPSPI_3_REG
         65   40 00001526 MC33772C_ProcessFrame
         66   41 0000C022 Normal_State
         67   42 00007004 cellVoltage
         68   43 00007008 current
         69   44 000061B5 doMeasurements
         70   45 0000C024 exit_code
         71   46 000060B3 fillNtcTable
         72   47 0000E200 g_bccData
         73   48 0000C02C g_sysClk
         74   49 0000151A get_cellVoltage
         75   4A 000014CE get_current
         76   4B 000014F4 get_stackVoltage
         77   4C 0000C020 inited
         78   4D 00001B2A main
         79   4E 0000C021 pCS
         80   4F 00004CC1 printInitialSettings
         81   50 00005083 printfaultRegisters
         82   51 00004C88 sendNops
         83   52 00001500 set_cellVoltage
         84   53 000014B4 set_current
         85   54 000014DA set_stackVoltage
         86   55 00007006 stackVoltage

  Summary

        1000 .CRT
        3000 .bss
        1000 .data
        1000 .debug_abbrev
        1000 .debug_aranges
        A000 .debug_info
        2000 .debug_line
        1000 .debug_str
        1000 .edata
        2000 .eh_frame
        1000 .idata
        2000 .rdata
        1000 .reloc
        6000 .text
        1000 .tls
