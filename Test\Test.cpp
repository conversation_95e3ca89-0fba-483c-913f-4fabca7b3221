﻿#include <systemc.h>
#include <boost/asio.hpp>
#include <iostream>
#include "message.pb.h"

using boost::asio::ip::udp;

SC_MODULE(SBC) {
    // Output để trace (n<PERSON><PERSON> cần)
    sc_out<int> sig_out;

    boost::asio::io_context io_ctx;
    udp::socket socket_;
    udp::endpoint server_ep;

    SC_HAS_PROCESS(SBC);

    SBC(sc_module_name name, const udp::endpoint & server_endpoint)
        : sc_module(name)
        , socket_(io_ctx)
        , server_ep(server_endpoint)
    {
        std::cout << "[SBC] Constructor executed\n";

        // open socket
        boost::system::error_code ec;
        socket_.open(udp::v4(), ec);
        if (ec) {
            std::cerr << "[SBC] Error opening socket: " << ec.message() << "\n";
            sc_stop();
            return;
        }

        // Thread sends peiodic 1us
        SC_THREAD(SBC_periodic_send);
        SC_THREAD(dummy);
    }

    void SBC_periodic_send() {
        int count = 0;
        while (true) {
            wait(1, SC_US);

            std::string text = "Hello from SBC at " + sc_time_stamp().to_string();
            send_msg(text);

            sig_out.write(++count); // Just for tracing/debug
        }
    }

    void dummy(){
        while (1) {
            wait(1, SC_US);
            std::cout << "Thread 2 : running" << std::endl;
        }
    }

    void send_msg(const std::string & text) {
        example::ChatMessage msg;
        msg.set_text(text);
        std::string out;
        msg.SerializeToString(&out);

        boost::system::error_code ec;
        std::size_t bytes = socket_.send_to(boost::asio::buffer(out), server_ep, 0, ec);
        if (!ec) {
            std::cout << sc_time_stamp() << " [SBC] Sent: " << text
                << "\nBytes sent: " << bytes << "\n";
        }
        else {
            std::cerr << "[SBC] Send error: " << ec.message() << "\n";
        }
    }

    void receive_msg() {
        std::array<char, 1024> recv_buffer{};
        boost::asio::ip::udp::endpoint sender_ep;
        boost::system::error_code ec;

        std::size_t bytes_received = socket_.receive_from(boost::asio::buffer(recv_buffer), sender_ep, 0, ec);

        if (!ec) {
            std::string input(recv_buffer.data(), bytes_received);
            example::ChatMessage msg;
            if (msg.ParseFromString(input)) {
                std::cout << sc_time_stamp() << " [SBC] Received: " << msg.text() << "\n";
            } else {
                std::cerr << "[SBC] Failed to parse message from Protobuf.\n";
            }
        } else {
            std::cerr << "[SBC] Receive error: " << ec.message() << "\n";
        }
    }
};

int sc_main(int argc, char* argv[]) {
    GOOGLE_PROTOBUF_VERIFY_VERSION;

    // Output trace signal
    sc_signal<int> sig_val;

    // Địa chỉ server bạn đã có
    auto server_address = boost::asio::ip::make_address("127.0.0.1");
    unsigned short server_port = 9000;
    udp::endpoint server_ep(server_address, server_port);

    // Tạo SBC module
    SBC sbc("sbc", server_ep);
    sbc.sig_out(sig_val);

    // Trace nếu cần
    sc_trace_file* tf = sc_create_vcd_trace_file("trace_simple");
    sc_trace(tf, sig_val, "sig_out");

    // Chạy mô phỏng 10 µs
    sc_start(10, SC_US);

    sc_close_vcd_trace_file(tf);
    google::protobuf::ShutdownProtobufLibrary();
    return 0;
}