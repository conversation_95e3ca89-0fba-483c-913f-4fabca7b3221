/*****************************************************************************

  Licensed to Accellera Systems Initiative Inc. (Accellera) under one or
  more contributor license agreements.  See the NOTICE file distributed
  with this work for additional information regarding copyright ownership.
  Accellera licenses this file to you under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with the
  License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
  implied.  See the License for the specific language governing
  permissions and limitations under the License.

 *****************************************************************************/

/*****************************************************************************

  systemc - Top-level namespace-based include file for the SystemC library.

  Original Author: Stan Y. Liao, Synopsys, Inc.

 *****************************************************************************/

/*****************************************************************************

  MODIFICATION LOG - modifiers, enter your name, affiliation, date and
  changes you are making here.

      Name, Affiliation, Date: Andy Goodrich, Forte Design Systems, 31 Mar 2005
  Description of Modification: This is the old systemc.h without usings.

 *****************************************************************************/

// $Log: systemc,v $
// Revision 1.6  2011/08/04 17:12:07  acg
//  Andy Goodrich: moved systemc and systemc.h back to src level because
//  of MSVC not doing an install.
//
// Revision 1.1  2011/07/02 13:26:48  acg
//  Andy Goodrich: moved header files up one level.
//
// Revision 1.4  2011/02/18 20:28:27  acg
//  Andy Goodrich: Updated Copyright.
//
// Revision 1.3  2011/01/20 16:52:09  acg
//  Andy Goodrich: changes for IEEE 1666 2011.
//
// Revision 1.2  2009/11/17 19:57:51  acg
//  Andy Goodrich: changes for boost.
//
// Revision *******  2006/12/15 20:20:04  acg
// SystemC 2.3
//
// Revision 1.5  2006/04/11 23:11:16  acg
//   Andy Goodrich: remove inclusions that exposed sc_method_process,
//   sc_thread_process, and sc_cthread_process internals.
//
// Revision 1.4  2006/01/25 00:48:29  acg
//  Andy Goodrich: added capture of CVS logging messages in the source.
//

#ifndef SYSTEMC_INCLUDED
#define SYSTEMC_INCLUDED

// include this file first
#include "sysc/kernel/sc_cmnhdr.h"

#include "sysc/kernel/sc_dynamic_processes.h"
#include "sysc/kernel/sc_except.h"
#include "sysc/kernel/sc_externs.h"
#include "sysc/kernel/sc_initializer_function.h"
#include "sysc/kernel/sc_module.h"
#include "sysc/kernel/sc_process_handle.h"
#include "sysc/kernel/sc_simcontext.h"
#include "sysc/kernel/sc_ver.h"

#include "sysc/communication/sc_buffer.h"
#include "sysc/communication/sc_clock.h"
#include "sysc/communication/sc_clock_ports.h"
#include "sysc/communication/sc_event_queue.h"
#include "sysc/communication/sc_export.h"
#include "sysc/communication/sc_fifo.h"
#include "sysc/communication/sc_fifo_ports.h"
#include "sysc/communication/sc_mutex.h"
#include "sysc/communication/sc_semaphore.h"
#include "sysc/communication/sc_signal.h"
#include "sysc/communication/sc_signal_ports.h"

#include "sysc/communication/sc_signal_resolved.h"
#include "sysc/communication/sc_signal_resolved_ports.h"
#include "sysc/communication/sc_signal_rv.h"
#include "sysc/communication/sc_signal_rv_ports.h"
#include "sysc/communication/sc_stub.h"

#include "sysc/datatypes/bit/sc_bit.h"
#include "sysc/datatypes/bit/sc_logic.h"
#include "sysc/datatypes/bit/sc_bv.h"
#include "sysc/datatypes/bit/sc_lv.h"

#include "sysc/datatypes/int/sc_signed.h"
#include "sysc/datatypes/int/sc_unsigned.h"

#include "sysc/datatypes/int/sc_bigint.h"
#include "sysc/datatypes/int/sc_biguint.h"
#include "sysc/datatypes/int/sc_int.h"
#include "sysc/datatypes/int/sc_uint.h"

#include "sysc/datatypes/int/sc_vector_utils.h"
#include "sysc/datatypes/int/sc_int_inlines.h"
#include "sysc/datatypes/int/sc_uint_inlines.h"
#include "sysc/datatypes/int/sc_signed_inlines.h"
#include "sysc/datatypes/int/sc_unsigned_inlines.h"
#include "sysc/datatypes/int/sc_bigint_inlines.h"
#include "sysc/datatypes/int/sc_biguint_inlines.h"
#include "sysc/datatypes/int/sc_signed_ops.h"
#include "sysc/datatypes/int/sc_big_ops.h"

#include "sysc/datatypes/misc/sc_concatref.h"

#ifdef SC_INCLUDE_FX
#   include "sysc/datatypes/fx/fx.h"
#endif // SC_INCLUDE_FX

#include "sysc/tracing/sc_trace.h"

#include "sysc/utils/sc_utils_ids.h"

#include "sysc/utils/sc_pvector.h"
#include "sysc/utils/sc_string_view.h"
#include "sysc/utils/sc_vector.h"
#include "sysc/utils/sc_string.h"

#endif // !defined(SYSTEMC_INCLUDED)

#ifdef SC_INCLUDE_EXTRA_STD_HEADERS
#    include <cstring>
#    include <sstream>
#endif // SC_INCLUDE_EXTRA_STD_HEADERS
