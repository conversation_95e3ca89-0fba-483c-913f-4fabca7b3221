LIBRARY libBCC_Model.dll 
EXPORTS 
BCC_FuseMirror_Read 
BCC_FuseMirror_Write 
BCC_GPIO_ReadPin 
BCC_GPIO_SetMode 
BCC_GPIO_SetOutput 
BCC_GUID_Read 
BCC_HardwareReset 
BCC_Init 
BCC_MCU_Assert 
BCC_MCU_GetSystemClockFreq 
BCC_MCU_ReadIntbPin 
BCC_MCU_StartTimeout 
BCC_MCU_TimeoutExpired 
BCC_MCU_TransferSpi 
BCC_MCU_TransferTpl 
BCC_MCU_WaitMs 
BCC_MCU_WaitSec 
BCC_MCU_WaitUs 
BCC_MCU_WriteCsbPin 
BCC_MCU_WriteEnPin 
BCC_MCU_WriteRstPin 
BCC_Meas_GetAnVoltage 
BCC_Meas_GetAnVoltages 
BCC_Meas_GetCellVoltage 
BCC_Meas_GetCellVoltages 
BCC_Meas_GetCoulombCounter 
BCC_Meas_GetIcTemperature 
BCC_Meas_GetIsenseVoltage 
BCC_Meas_GetRawValues 
BCC_Meas_GetStackVoltage 
BCC_Meas_IsConverting 
BCC_Meas_StartAndWait 
BCC_Meas_StartConversion 
BCC_Meas_StartConversionGlobal 
BCC_Reg_Read 
BCC_Reg_ReadSpi 
BCC_Reg_ReadTpl 
BCC_Reg_Update 
BCC_Reg_Write 
BCC_Reg_WriteGlobal 
BCC_Reg_WriteGlobalTpl 
BCC_Reg_WriteSpi 
BCC_Reg_WriteTpl 
BCC_SendNop 
BCC_SendNopSpi 
BCC_SendNopTpl 
BCC_Sleep 
BCC_SoftwareReset 
BCC_TPL_Disable 
BCC_TPL_Enable 
BCC_WakeUp 
BCC_processing 
LPIT0_Ch3_IRQHandler 
LPSPI_0_REG 
LPSPI_1_REG 
LPSPI_2_REG 
LPSPI_3_REG 
MC33772C_ProcessFrame 
Normal_State 
cellVoltage 
current 
doMeasurements 
exit_code 
fillNtcTable 
g_bccData 
g_sysClk 
get_cellVoltage 
get_current 
get_stackVoltage 
inited 
main 
pCS 
printInitialSettings 
printfaultRegisters 
sendNops 
set_cellVoltage 
set_current 
set_stackVoltage 
stackVoltage 
